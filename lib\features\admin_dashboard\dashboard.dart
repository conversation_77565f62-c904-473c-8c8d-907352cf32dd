// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts_buyer/core/models/offer_model.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/order_details_dialog.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/user_activity_dialog.dart';
import 'package:intl/intl.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isLoading = true;
  String? _adminUid;
  List<WebsiteModel> _websites = [];
  List<OrderModel> _orders = [];
  List<UserModel> _users = [];
  List<OfferModel> _offers = [];
  List<Map<String, dynamic>> _topPublishers = [];
  Map<String, int> _categories = {};
  StreamSubscription<QuerySnapshot>? _websitesSubscription;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<QuerySnapshot>? _usersSubscription;
  StreamSubscription<QuerySnapshot>? _offersSubscription;

  // Pagination states
  int _publishersPage = 1;
  int _categoriesPage = 1;
  int _websitesPage = 1;
  int _ordersPage = 1;
  int _usersPage = 1;
  final int _itemsPerPage = 5;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );
      // Uncomment for production
      if (userData == null || userData['isAdmin'] != true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Access denied: Admins only'),
              backgroundColor: Colors.red,
            ),
          );
          context.go('/login');
        }
        return;
      }

      if (mounted) {
        setState(() {
          _adminUid = user.uid;
          _isLoading = true;
        });
        _subscribeToData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking admin access: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _subscribeToData() {
    // Subscribe to websites
    _websitesSubscription = FirebaseFirestore.instance
        .collection('websites')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) {
      final websites = snapshot.docs.map((doc) {
        final data = doc.data();
        return WebsiteModel.fromMap(data..['websiteId'] = doc.id);
      }).toList();

      setState(() {
        _websites = websites;
        _updateCategories();
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading websites: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to offers
    _offersSubscription = FirebaseFirestore.instance
        .collection('offers')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen((snapshot) {
      final offers = snapshot.docs.map((doc) {
        return OfferModel.fromMap(doc.data(), doc.id);
      }).toList();

      setState(() {
        _offers = offers;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading offers: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to orders
    _ordersSubscription = FirebaseFirestore.instance
        .collection('orders')
        .orderBy('orderDate', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) async {
      final orders = snapshot.docs.map((doc) {
        final data = doc.data();
        return OrderModel.fromMap(data..['orderId'] = doc.id);
      }).toList();

      await _updateTopPublishers(orders);

      setState(() {
        _orders = orders;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading orders: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to users
    _usersSubscription = FirebaseFirestore.instance
        .collection('users')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) {
      final users = snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromMap(data, doc.id);
      }).toList();

      setState(() {
        _users = users;
        _isLoading = false;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading users: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });
  }

  Future<void> _updateTopPublishers(List<OrderModel> orders) async {
    // Count completed orders per publisher
    final publisherOrderCounts = <String, int>{};
    for (var order in orders.where((o) => o.status == 'Completed')) {
      if (order.publisherId != null) {
        publisherOrderCounts[order.publisherId!] =
            (publisherOrderCounts[order.publisherId!] ?? 0) + 1;
      }
    }

    // Get top publishers
    final topPublisherIds = publisherOrderCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final publisherIds = topPublisherIds.map((e) => e.key).toList();

    // Fetch publisher details
    final topPublishers = <Map<String, dynamic>>[];
    for (var publisherId in publisherIds) {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: publisherId,
      );
      if (userData != null) {
        final user = UserModel.fromMap(userData, publisherId);
        topPublishers.add({
          'user': user,
          'completedOrders': publisherOrderCounts[publisherId]!,
        });
      }
    }

    setState(() {
      _topPublishers = topPublishers;
    });
  }

  void _updateCategories() {
    final categories = <String, int>{};
    for (var website in _websites) {
      final websiteCategories = website.categories.isNotEmpty
          ? website.categories
          : ['Uncategorized'];
      for (var category in websiteCategories) {
        categories[category] = (categories[category] ?? 0) + 1;
      }
    }
    setState(() {
      _categories = categories;
    });
  }

  void _viewWebsiteDetails(WebsiteModel website) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(website.url),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Status: ${website.status}'),
              Text(
                  'Categories: ${website.categories.isNotEmpty ? website.categories.join(", ") : "N/A"}'),
              Text('DA: ${website.da}'),
              Text('DR: ${website.dr}'),
              Text('Traffic: ${website.traffic}'),
              Text('Price: \$${website.pricing.toStringAsFixed(2)}'),
              Text('Created: ${_formatDate(website.createdAt)}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewOrderDetails(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => OrderDetailsDialog(
        order: order,
        website: null,
        buyerName: 'Unknown',
        buyerEmail: 'N/A',
        publisher: null,
        onChangeStatus: (status) async {
          final now = Timestamp.now();
          final statusDetail = '$status by Admin';

          // Set appropriate date fields based on status
          final Map<String, dynamic> data = {
            'status': status,
            'lastUpdated': now,
            'actionBy': _adminUid,
            'actionTimestamp': now,
            'statusDetail': statusDetail,
          };

          // Add specific date fields based on status
          if (status == 'In Progress') {
            data['inProgressDate'] = now;
          } else if (status == 'Approved') {
            data['approvalDate'] = now;
          } else if (status == 'Completed') {
            data['completionDate'] = now;
          }

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: data,
          );
        },
        onAssignPublisher: (publisherId) async {
          final now = Timestamp.now();
          final action = publisherId != null
              ? 'Publisher assigned'
              : 'Publisher unassigned';
          final actionDetail = '$action by Admin';

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: {
              'publisherId': publisherId,
              'lastUpdated': now,
              'actionBy': _adminUid,
              'actionTimestamp': now,
              'statusDetail': actionDetail,
            },
          );
        },
        onUpdateDispute: (isDisputed, note, disputeStatus) async {
          final now = Timestamp.now();
          final disputeAction =
              isDisputed ? 'Dispute flagged' : 'Dispute resolved';
          final disputeStatusDetail = '$disputeAction by Admin';

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: {
              'isDisputed': isDisputed,
              'disputeNote': note,
              'disputeStatus': disputeStatus,
              'lastUpdated': now,
              'actionBy': _adminUid,
              'actionTimestamp': now,
              'statusDetail': disputeStatusDetail,
            },
          );
        },
        firestoreService: _firestoreService,
      ),
    );
  }

  void _viewUserActivity(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => UserActivityDialog(
        user: user,
        firestoreService: _firestoreService,
      ),
    );
  }

  @override
  void dispose() {
    _websitesSubscription?.cancel();
    _ordersSubscription?.cancel();
    _usersSubscription?.cancel();
    _offersSubscription?.cancel();
    super.dispose();
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
    VoidCallback? onTap,
  }) {
    final isSmallScreen = cardWidth < 200;

    // Determine which gradient to use based on the color
    LinearGradient gradient;
    if (color == AppColors.blue ||
        color == AppColors.primaryBlue ||
        color == Colors.blue.shade700) {
      gradient = AppColors.blueGradient;
    } else if (color == AppColors.green || color == Colors.green.shade600) {
      gradient = AppColors.greenGradient;
    } else if (color == AppColors.yellow || color == AppColors.yellow) {
      gradient = AppColors.yellowGradient;
    } else if (color == AppColors.red || color == Colors.red.shade600) {
      gradient = AppColors.redGradient;
    } else {
      gradient = AppColors.blueGradient;
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: cardWidth,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.dark,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child:
                      Icon(icon, color: color, size: isSmallScreen ? 20 : 24),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 24 : 32,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 13 : 15,
      fontWeight: FontWeight.w600,
      color: AppColors.blueDark,
      letterSpacing: 0.5,
    );
  }

  Widget _buildPaginationControls(
      {required int currentPage,
      required int totalItems,
      required Function(int) onPageChanged,
      required bool isSmallScreen}) {
    final totalPages = (totalItems / _itemsPerPage).ceil();
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: AppColors.blue.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(
              Icons.chevron_left,
              size: isSmallScreen ? 20 : 24,
              color: currentPage > 1 ? AppColors.blue : AppColors.grey,
            ),
            style: IconButton.styleFrom(
              backgroundColor: currentPage > 1
                  ? AppColors.blueLightest
                  : Colors.grey.shade100,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed:
                currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'Page $currentPage of $totalPages',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w500,
                color: AppColors.dark,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.chevron_right,
              size: isSmallScreen ? 20 : 24,
              color: currentPage < totalPages ? AppColors.blue : AppColors.grey,
            ),
            style: IconButton.styleFrom(
              backgroundColor: currentPage < totalPages
                  ? AppColors.blueLightest
                  : Colors.grey.shade100,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: currentPage < totalPages
                ? () => onPageChanged(currentPage + 1)
                : null,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 600;
        final isWideScreen = screenWidth > 800;

        if (_auth.currentUser == null || _adminUid == null) {
          return const Center(
              child: Text('Please sign in to view the dashboard'));
        }

        final crossAxisCount = screenWidth < 600
            ? 1
            : screenWidth < 900
                ? 2
                : 5;
        final cardWidth = screenWidth < 600
            ? screenWidth * 0.9
            : screenWidth / crossAxisCount - 32;

        // Paginated data
        final publishersStartIndex = (_publishersPage - 1) * _itemsPerPage;
        final publishersEndIndex = publishersStartIndex + _itemsPerPage;
        final paginatedPublishers = _topPublishers
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= publishersStartIndex &&
                entry.key < publishersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final categoriesStartIndex = (_categoriesPage - 1) * _itemsPerPage;
        final categoriesEndIndex = categoriesStartIndex + _itemsPerPage;
        final paginatedCategories = _categories.entries
            .toList()
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= categoriesStartIndex &&
                entry.key < categoriesEndIndex)
            .map((entry) => entry.value)
            .toList();

        final websitesStartIndex = (_websitesPage - 1) * _itemsPerPage;
        final websitesEndIndex = websitesStartIndex + _itemsPerPage;
        final paginatedWebsites = _websites
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= websitesStartIndex && entry.key < websitesEndIndex)
            .map((entry) => entry.value)
            .toList();

        final ordersStartIndex = (_ordersPage - 1) * _itemsPerPage;
        final ordersEndIndex = ordersStartIndex + _itemsPerPage;
        final paginatedOrders = _orders
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= ordersStartIndex && entry.key < ordersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final usersStartIndex = (_usersPage - 1) * _itemsPerPage;
        final usersEndIndex = usersStartIndex + _itemsPerPage;
        final paginatedUsers = _users
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= usersStartIndex && entry.key < usersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final completedOrders =
            _orders.where((o) => o.status == 'Completed').length;

        return Scaffold(
          backgroundColor: AppColors.backgroundColor,
          body: _isLoading
              ? Center(
                  child: CircularProgressIndicator(
                    color: AppColors.blue,
                  ),
                )
              : SingleChildScrollView(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.blue.withAlpha(15),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Admin Dashboard',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: isSmallScreen ? 24 : 32,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.dark,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Welcome back, Admin',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: isSmallScreen ? 14 : 16,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.blueLightest,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.dashboard_customize,
                                color: AppColors.blue,
                                size: isSmallScreen ? 24 : 28,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      GridView.count(
                        crossAxisCount: crossAxisCount,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(
                            vertical: 16, horizontal: 8),
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 2,
                        children: [
                          _buildSummaryCard(
                            title: 'Total Websites',
                            value: _websites.length.toString(),
                            color: AppColors.blue,
                            icon: Icons.web,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/websites'),
                          ),
                          _buildSummaryCard(
                            title: 'Pending Websites',
                            value: _websites
                                .where((w) => w.status == 'Pending')
                                .length
                                .toString(),
                            color: AppColors.yellow,
                            icon: Icons.hourglass_empty,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/websites'),
                          ),
                          _buildSummaryCard(
                            title: 'Total Orders',
                            value: _orders.length.toString(),
                            color: AppColors.green,
                            icon: Icons.shopping_cart,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Completed Orders',
                            value: completedOrders.toString(),
                            color: AppColors.green,
                            icon: Icons.check_circle,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Disputed Orders',
                            value: _orders
                                .where((o) => o.isDisputed ?? false)
                                .length
                                .toString(),
                            color: AppColors.red,
                            icon: Icons.warning,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Total Users',
                            value: _users.length.toString(),
                            color: Colors.blue.shade700,
                            icon: Icons.people,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Active Users',
                            value: _users
                                .where((u) => !u.isBlocked)
                                .length
                                .toString(),
                            color: Colors.orange.shade700,
                            icon: Icons.person,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Publishers',
                            value: _users
                                .where((u) => u.isPublisher)
                                .length
                                .toString(),
                            color: Colors.green.shade600,
                            icon: Icons.publish,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Blocked Users',
                            value: _users
                                .where((u) => u.isBlocked)
                                .length
                                .toString(),
                            color: Colors.red.shade600,
                            icon: Icons.block,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Manage Offers',
                            value: _offers
                                .where((o) => o.isActive)
                                .length
                                .toString(),
                            color: Colors.purple.shade600,
                            icon: Icons.local_offer,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/offers'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Top Publishers',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _topPublishers.isEmpty
                          ? Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.publish,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No publishers found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 16 : 18,
                                      color: AppColors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Card(
                                    elevation: 0,
                                    margin:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.blue.withAlpha(10),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: DataTable(
                                          columnSpacing: isWideScreen
                                              ? 40
                                              : isSmallScreen
                                                  ? 20
                                                  : 30,
                                          headingRowColor:
                                              MaterialStateProperty.all(
                                                  AppColors.blueLightest),
                                          dataRowColor:
                                              MaterialStateProperty.resolveWith(
                                            (states) {
                                              if (states.contains(
                                                  MaterialState.hovered)) {
                                                return AppColors.blueLightest
                                                    .withAlpha(50);
                                              }
                                              return Colors.white;
                                            },
                                          ),
                                          border: TableBorder(
                                            horizontalInside: BorderSide(
                                              color: AppColors.blueLight
                                                  .withAlpha(50),
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          columns: [
                                            DataColumn(
                                              label: Text('Name',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Email',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Completed Orders',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Actions',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                          ],
                                          rows: paginatedPublishers
                                              .map((publisher) =>
                                                  _buildPublisherDataRow(
                                                      publisher, isSmallScreen))
                                              .toList(),
                                        ),
                                      ),
                                    )),
                                _buildPaginationControls(
                                  currentPage: _publishersPage,
                                  totalItems: _topPublishers.length,
                                  onPageChanged: (page) =>
                                      setState(() => _publishersPage = page),
                                  isSmallScreen: isSmallScreen,
                                ),
                                const SizedBox(height: 8),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: ElevatedButton.icon(
                                    onPressed: () => context.go('/users'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blueLightest,
                                      foregroundColor: AppColors.blueDark,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    label: Text(
                                      'See More',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Website Categories',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _categories.isEmpty
                          ? Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.category,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No categories found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 16 : 18,
                                      color: AppColors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Card(
                                    elevation: 0,
                                    margin:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.blue.withAlpha(10),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: DataTable(
                                          columnSpacing: isWideScreen
                                              ? 40
                                              : isSmallScreen
                                                  ? 20
                                                  : 30,
                                          headingRowColor:
                                              MaterialStateProperty.all(
                                                  AppColors.blueLightest),
                                          dataRowColor:
                                              MaterialStateProperty.resolveWith(
                                            (states) {
                                              if (states.contains(
                                                  MaterialState.hovered)) {
                                                return AppColors.blueLightest
                                                    .withAlpha(50);
                                              }
                                              return Colors.white;
                                            },
                                          ),
                                          border: TableBorder(
                                            horizontalInside: BorderSide(
                                              color: AppColors.blueLight
                                                  .withAlpha(50),
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          columns: [
                                            DataColumn(
                                              label: Text('Category',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Websites',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                          ],
                                          rows: paginatedCategories
                                              .map((entry) =>
                                                  _buildCategoryDataRow(
                                                      entry, isSmallScreen))
                                              .toList(),
                                        ),
                                      ),
                                    )),
                                _buildPaginationControls(
                                  currentPage: _categoriesPage,
                                  totalItems: _categories.length,
                                  onPageChanged: (page) =>
                                      setState(() => _categoriesPage = page),
                                  isSmallScreen: isSmallScreen,
                                ),
                                const SizedBox(height: 8),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: ElevatedButton.icon(
                                    onPressed: () => context.go('/websites'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blueLightest,
                                      foregroundColor: AppColors.blueDark,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    label: Text(
                                      'See More',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recent Websites',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      paginatedWebsites.isEmpty
                          ? Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.web,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No websites found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 16 : 18,
                                      color: AppColors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Card(
                                    elevation: 0,
                                    margin:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.blue.withAlpha(10),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: DataTable(
                                          columnSpacing: isWideScreen
                                              ? 40
                                              : isSmallScreen
                                                  ? 20
                                                  : 30,
                                          headingRowColor:
                                              MaterialStateProperty.all(
                                                  AppColors.blueLightest),
                                          dataRowColor:
                                              MaterialStateProperty.resolveWith(
                                            (states) {
                                              if (states.contains(
                                                  MaterialState.hovered)) {
                                                return AppColors.blueLightest
                                                    .withAlpha(50);
                                              }
                                              return Colors.white;
                                            },
                                          ),
                                          border: TableBorder(
                                            horizontalInside: BorderSide(
                                              color: AppColors.blueLight
                                                  .withAlpha(50),
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          columns: [
                                            DataColumn(
                                              label: Text('URL',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Status',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Price',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Created',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Actions',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                          ],
                                          rows: paginatedWebsites
                                              .map((website) =>
                                                  _buildWebsiteDataRow(
                                                      website, isSmallScreen))
                                              .toList(),
                                        ),
                                      ),
                                    )),
                                _buildPaginationControls(
                                  currentPage: _websitesPage,
                                  totalItems: _websites.length,
                                  onPageChanged: (page) =>
                                      setState(() => _websitesPage = page),
                                  isSmallScreen: isSmallScreen,
                                ),
                                const SizedBox(height: 8),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: ElevatedButton.icon(
                                    onPressed: () => context.go('/websites'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blueLightest,
                                      foregroundColor: AppColors.blueDark,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    label: Text(
                                      'See More',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recent Orders',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      paginatedOrders.isEmpty
                          ? Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.shopping_cart,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No orders found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 16 : 18,
                                      color: AppColors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Card(
                                    elevation: 0,
                                    margin:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.blue.withAlpha(10),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: DataTable(
                                          columnSpacing: isWideScreen
                                              ? 40
                                              : isSmallScreen
                                                  ? 20
                                                  : 30,
                                          headingRowColor:
                                              MaterialStateProperty.all(
                                                  AppColors.blueLightest),
                                          dataRowColor:
                                              MaterialStateProperty.resolveWith(
                                            (states) {
                                              if (states.contains(
                                                  MaterialState.hovered)) {
                                                return AppColors.blueLightest
                                                    .withAlpha(50);
                                              }
                                              return Colors.white;
                                            },
                                          ),
                                          border: TableBorder(
                                            horizontalInside: BorderSide(
                                              color: AppColors.blueLight
                                                  .withAlpha(50),
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          columns: [
                                            DataColumn(
                                              label: Text('Order ID',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Status',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Price',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Ordered',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                            DataColumn(
                                              label: Text('Actions',
                                                  style: _headerStyle(
                                                      isSmallScreen)),
                                            ),
                                          ],
                                          rows: paginatedOrders
                                              .map((order) =>
                                                  _buildOrderDataRow(
                                                      order, isSmallScreen))
                                              .toList(),
                                        ),
                                      ),
                                    )),
                                _buildPaginationControls(
                                  currentPage: _ordersPage,
                                  totalItems: _orders.length,
                                  onPageChanged: (page) =>
                                      setState(() => _ordersPage = page),
                                  isSmallScreen: isSmallScreen,
                                ),
                                const SizedBox(height: 8),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: ElevatedButton.icon(
                                    onPressed: () => context.go('/orders'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blueLightest,
                                      foregroundColor: AppColors.blueDark,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    label: Text(
                                      'See More',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recent Users',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      paginatedUsers.isEmpty
                          ? Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.person,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No users found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 16 : 18,
                                      color: AppColors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Card(
                                  elevation: 0,
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 8),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.blue.withAlpha(10),
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: DataTable(
                                        columnSpacing: isWideScreen
                                            ? 40
                                            : isSmallScreen
                                                ? 20
                                                : 30,
                                        headingRowColor:
                                            MaterialStateProperty.all(
                                                AppColors.blueLightest),
                                        dataRowColor:
                                            MaterialStateProperty.resolveWith(
                                          (states) {
                                            if (states.contains(
                                                MaterialState.hovered)) {
                                              return AppColors.blueLightest
                                                  .withAlpha(50);
                                            }
                                            return Colors.white;
                                          },
                                        ),
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: AppColors.blueLight
                                                .withAlpha(50),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        columns: [
                                          DataColumn(
                                            label: Text('Name',
                                                style: _headerStyle(
                                                    isSmallScreen)),
                                          ),
                                          DataColumn(
                                            label: Text('Email',
                                                style: _headerStyle(
                                                    isSmallScreen)),
                                          ),
                                          DataColumn(
                                            label: Text('Role',
                                                style: _headerStyle(
                                                    isSmallScreen)),
                                          ),
                                          DataColumn(
                                            label: Text('Created',
                                                style: _headerStyle(
                                                    isSmallScreen)),
                                          ),
                                          DataColumn(
                                            label: Text('Actions',
                                                style: _headerStyle(
                                                    isSmallScreen)),
                                          ),
                                        ],
                                        rows: paginatedUsers
                                            .map((user) => _buildUserDataRow(
                                                user, isSmallScreen))
                                            .toList(),
                                      ),
                                    ),
                                  ),
                                ),
                                _buildPaginationControls(
                                  currentPage: _usersPage,
                                  totalItems: _users.length,
                                  onPageChanged: (page) =>
                                      setState(() => _usersPage = page),
                                  isSmallScreen: isSmallScreen,
                                ),
                                const SizedBox(height: 8),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: ElevatedButton.icon(
                                    onPressed: () => context.go('/users'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blueLightest,
                                      foregroundColor: AppColors.blueDark,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    label: Text(
                                      'See More',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
        );
      },
    );
  }

  DataRow _buildWebsiteDataRow(WebsiteModel website, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            website.url.length > 20
                ? '${website.url.substring(0, 17)}...'
                : website.url,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 15,
              fontWeight: FontWeight.w500,
              color: AppColors.blueDark,
            ),
          ),
        ),
        DataCell(
          Text(
            website.status,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: website.status == 'Approved'
                  ? AppColors.green
                  : website.status == 'Pending'
                      ? Colors.orange
                      : AppColors.red,
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${website.pricing.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(website.createdAt),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(Icons.visibility,
                color: AppColors.blue, size: isSmallScreen ? 20 : 24),
            onPressed: () => _viewWebsiteDetails(website),
          ),
        ),
      ],
    );
  }

  DataRow _buildOrderDataRow(OrderModel order, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            order.orderId ?? 'N/A',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: AppColors.blue,
            ),
          ),
        ),
        DataCell(
          Text(
            order.status,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: order.status == 'Completed'
                  ? AppColors.green
                  : order.status == 'In Progress' || order.status == 'Pending'
                      ? Colors.orange
                      : AppColors.red,
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${order.totalPrice.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(order.orderDate),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(Icons.visibility,
                color: AppColors.blue, size: isSmallScreen ? 20 : 24),
            onPressed: () => _viewOrderDetails(order),
          ),
        ),
      ],
    );
  }

  DataRow _buildUserDataRow(UserModel user, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            user.name,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            user.email,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            user.isPublisher ? 'Publisher' : 'Advertiser',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: AppColors.blue,
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(user.createdAt),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(Icons.visibility,
                color: AppColors.blue, size: isSmallScreen ? 20 : 24),
            onPressed: () => _viewUserActivity(user),
          ),
        ),
      ],
    );
  }

  DataRow _buildPublisherDataRow(
      Map<String, dynamic> publisherData, bool isSmallScreen) {
    final user = publisherData['user'] as UserModel;
    final completedOrders = publisherData['completedOrders'] as int;

    return DataRow(
      cells: [
        DataCell(
          Text(
            user.name,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            user.email,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            completedOrders.toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(Icons.visibility,
                color: AppColors.blue, size: isSmallScreen ? 20 : 24),
            onPressed: () => _viewUserActivity(user),
          ),
        ),
      ],
    );
  }

  DataRow _buildCategoryDataRow(
      MapEntry<String, int> category, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            category.key,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            category.value.toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
      ],
    );
  }
}
